const axios = require('axios');

/**
 * Extracts plain text from a Splitbee/unofficial Notion API title property.
 * Example title format: [["Text segment 1"], ["Text segment 2 with "], ["bold", [["b"]]], [" and link", [["a", "http://example.com"]]]]
 * @param {Array<Array<any>>} titleProperty - The title property from a block.
 * @returns {string} - Concatenated plain text.
 */
function getTextFromTitleProperty(titleProperty) {
    if (!titleProperty || !Array.isArray(titleProperty)) {
        return '';
    }
    return titleProperty.map(segment => {
        if (Array.isArray(segment)) {
            return segment[0]; // The first element is usually the text
        }
        return '';
    }).join('');
}


/**
 * Recursively extracts text content from a Notion block and its children,
 * using data fetched from an unofficial Notion API like Splitbee's.
 * @param {string} blockId - The ID of the block to process.
 * @param {object} allBlocksData - An object containing all blocks of the page, keyed by block ID.
 * @param {Set<string>} visitedPages - A set of already visited page IDs to prevent infinite loops.
 * @returns {Promise<string>} - A promise that resolves to the concatenated text content.
 */
async function getTextFromBlock(blockId, allBlocksData, visitedPages = new Set()) {
    const block = allBlocksData[blockId];
    if (!block || !block.value) {
        // console.warn(`Block with ID ${blockId} not found or has no value in allBlocksData.`);
        return '';
    }

    const blockValue = block.value;
    let ownText = ''; // Text from the current block itself

    // Determine ownText based on block type
    switch (blockValue.type) {
        case 'page': // The main page block, its title is usually significant.
        case 'header': // h1
        case 'sub_header': // h2
        case 'sub_sub_header': // h3
        case 'text': // paragraph
        case 'bulleted_list': // For list items, the text is its content.
        case 'numbered_list': // For list items, the text is its content.
        case 'quote':
        case 'to_do': // Text of the to-do item.
        case 'toggle': // Text of the toggle itself. Children are processed below.
        case 'callout':
            // Most text-based blocks use properties.title
            if (blockValue.properties && blockValue.properties.title) {
                ownText = getTextFromTitleProperty(blockValue.properties.title);
            }
            break;
        case 'code':
            // Code blocks might store their content in properties.title or sometimes properties.rich_text
            if (blockValue.properties && blockValue.properties.title) {
                ownText = getTextFromTitleProperty(blockValue.properties.title);
            } else if (blockValue.properties && blockValue.properties.rich_text) { // Fallback
                ownText = blockValue.properties.rich_text.map(rt => rt.plain_text || rt.text?.content || '').join('');
            }
            break;
        case 'child_page': // This block is a link to another page.
            if (blockValue.properties && blockValue.properties.title) {
                ownText = getTextFromTitleProperty(blockValue.properties.title); // This is the title of the link.
            }
            // Fetch the content of the child page if we haven't visited it yet
            if (blockValue.id && !visitedPages.has(blockValue.id)) {
                try {
                    console.log(`Fetching content for child_page: ${blockValue.id}`);
                    visitedPages.add(blockValue.id);
                    const childPageContent = await extractTextWithNotionAPI(blockValue.id, visitedPages);
                    if (childPageContent && childPageContent.trim()) {
                        ownText = (ownText ? ownText + '\n' : '') + childPageContent;
                    }
                } catch (error) {
                    console.error(`Failed to fetch child_page content for ${blockValue.id}:`, error.message);
                    // Continue with just the title if fetching fails
                }
            }
            break;
        case 'child_database': // This block is a link to a database.
            if (blockValue.properties && blockValue.properties.title) {
                ownText = getTextFromTitleProperty(blockValue.properties.title); // Title of the database link.
            }
            // Fetch the content of the child database if we haven't visited it yet
            if (blockValue.id && !visitedPages.has(blockValue.id)) {
                try {
                    console.log(`Fetching content for child_database: ${blockValue.id}`);
                    visitedPages.add(blockValue.id);
                    const childDatabaseContent = await extractTextWithNotionAPI(blockValue.id, visitedPages);
                    if (childDatabaseContent && childDatabaseContent.trim()) {
                        ownText = (ownText ? ownText + '\n' : '') + childDatabaseContent;
                    }
                } catch (error) {
                    console.error(`Failed to fetch child_database content for ${blockValue.id}:`, error.message);
                    // Continue with just the title if fetching fails
                }
            }
            break;
        case 'collection_view_page': // A page that is primarily a collection view.
        case 'collection_view':      // An inline collection view (e.g., a table in a page).
            // Attempt to get the title of the collection view itself.
            if (blockValue.name) { // Name of the view itself
                ownText = getTextFromTitleProperty(blockValue.name);
            } else if (blockValue.collection_id && allBlocksData[blockValue.collection_id]?.value?.name) {
                // The name/title might be on the 'collection' block definition itself
                ownText = getTextFromTitleProperty(allBlocksData[blockValue.collection_id].value.name);
            } else if (blockValue.properties && blockValue.properties.title) {
                // Fallback if the view block itself has a title property (less common for the view itself)
                ownText = getTextFromTitleProperty(blockValue.properties.title);
            }

            // For collection_view_page, fetch the page content if we haven't visited it yet
            if (blockValue.id && !visitedPages.has(blockValue.id)) {
                try {
                    console.log(`Fetching content for collection_view_page: ${blockValue.id}`);
                    visitedPages.add(blockValue.id);
                    const collectionPageContent = await extractTextWithNotionAPI(blockValue.id, visitedPages);
                    if (collectionPageContent && collectionPageContent.trim()) {
                        ownText = (ownText ? ownText + '\n' : '') + collectionPageContent;
                    }
                } catch (error) {
                    console.error(`Failed to fetch collection_view_page content for ${blockValue.id}:`, error.message);
                    // Continue with just the title if fetching fails
                }
            }
            // Child blocks of a collection_view block itself (if any, like a caption) will be processed by the generic child processing.
            break;
        // Blocks that primarily structure content, their text comes from children:
        case 'column_list': // Contains 'column' blocks as children.
        case 'column':      // Contains other content blocks as children.
            // No 'ownText' for these structural blocks; their content is their children.
            break;
        // Blocks that might have captions or titles but are not primarily text containers for this function's purpose:
        case 'image':
        case 'video':
        case 'embed':
        case 'file':
        case 'bookmark':
        case 'table_of_contents': // Generated, not direct text input here.
        case 'equation': // Could be text, but needs specific parsing (e.g., LaTeX).
        case 'divider': // No text.
            // For these, we could try to extract a caption if the API provides it, e.g., blockValue.properties.caption
            if (blockValue.properties && blockValue.properties.caption) {
                ownText = getTextFromTitleProperty(blockValue.properties.caption);
            } else if (blockValue.properties && blockValue.properties.title) { // Some (like bookmark) might have a title
                ownText = getTextFromTitleProperty(blockValue.properties.title);
            }
            // console.log(`Type ${blockValue.type} might have caption/title: ${ownText}`);
            break;
        default:
            // Generic fallback for any other block type not explicitly handled.
            if (blockValue.properties && blockValue.properties.title) {
                ownText = getTextFromTitleProperty(blockValue.properties.title);
                // console.log(`Default handling for type ${blockValue.type} with title: ${ownText}`);
            } else {
                // console.log(`Unhandled or text-less block type in switch: ${blockValue.type}`);
            }
            break;
    }

    let childrenText = '';
    // Process child blocks if they exist (often in blockValue.content or blockValue.page_content for some APIs)
    const childBlockIds = blockValue.content || blockValue.page_content; // page_content is sometimes used for children of a 'page' block
    if (childBlockIds && Array.isArray(childBlockIds)) {
        for (const childBlockId of childBlockIds) {
            const childTextContent = await getTextFromBlock(childBlockId, allBlocksData, visitedPages);
            if (childTextContent && childTextContent.trim()) {
                childrenText += childTextContent; // childTextContent should already have newlines if needed
            }
        }
    }

    let combinedText = '';
    if (ownText && ownText.trim()) {
        combinedText += ownText.trim() + '\n';
    }
    // Only add childrenText if it actually contains something, to avoid extra newlines.
    if (childrenText && childrenText.trim()) {
        combinedText += childrenText;
    }

    return combinedText;
}

/**
 * Extracts all text content from a public Notion page using its ID via notion-api.splitbee.io.
 * @param {string} pageId - The ID of the Notion page (UUID format).
 * @param {Set<string>} visitedPages - A set of already visited page IDs to prevent infinite loops.
 * @returns {Promise<string>} - A promise that resolves to the full text content of the page.
 */
async function extractTextWithNotionAPI(pageId, visitedPages = new Set()) {
    if (!pageId) {
        throw new Error('Invalid Page ID provided.');
    }

    const apiUrl = `https://notion-api.splitbee.io/v1/page/${pageId}`;

    try {
        const response = await axios.get(apiUrl);
        const allBlocksData = response.data; // This is expected to be an object: { blockId: blockData, ... }

        if (!allBlocksData || typeof allBlocksData !== 'object' || Object.keys(allBlocksData).length === 0) {
            throw new Error(`No data received from Splitbee API for page ID ${pageId}. The page might be private, non-existent, or the API structure might have changed.`);
        }

        // The pageId itself is the ID of the root block (often of type 'page')
        // We need to find the actual root block ID from the response,
        // as pageId might be a "clean" ID, and the API might use a hyphenated version as the key.
        // Or, the pageId itself is the key to the main page block.
        // Let's assume pageId is a valid key in allBlocksData for the root content.
        // The Splitbee API usually returns the page block itself as one of the entries.
        // The children of this page block are listed in its 'content' array.

        // Find the main page block. Its 'parent_id' would be the workspace or not set in a way that indicates it's a top-level page block.
        // Or, more simply, the pageId itself should be a key in allBlocksData representing the page.
        let rootBlockId = pageId; // Start by assuming the provided pageId is the direct key
        if (!allBlocksData[rootBlockId]) {
            // Try to find a block that looks like the main page container if direct ID lookup fails
            // This can be tricky as there isn't a single guaranteed way.
            // Often, the block with type 'page' and matching the ID (or whose content is the page) is the one.
            // For now, we'll proceed assuming pageId is the key to the primary page block data.
            // A more robust way might be to iterate keys and find the block of type 'page' that matches the ID.
            console.warn(`Page ID ${pageId} not directly found as a key in Splitbee response. Attempting to find a matching page block.`);
            const potentialRootKey = Object.keys(allBlocksData).find(key =>
                allBlocksData[key]?.value?.id === pageId && allBlocksData[key]?.value?.type === 'page'
            );
            if (potentialRootKey) {
                rootBlockId = potentialRootKey;
            } else {
                // If still not found, try the first block that is of type 'page'
                const firstPageBlockKey = Object.keys(allBlocksData).find(key => allBlocksData[key]?.value?.type === 'page');
                if (firstPageBlockKey) {
                    console.warn(`Using first block of type 'page' as root: ${firstPageBlockKey}`);
                    rootBlockId = firstPageBlockKey;
                } else {
                    throw new Error(`Could not determine the root block for page ID ${pageId} from Splitbee API response.`);
                }
            }
        }

        // Add the current page to visited pages to prevent re-processing
        visitedPages.add(pageId);
        return await getTextFromBlock(rootBlockId, allBlocksData, visitedPages);
    } catch (error) {
        console.error(`Error fetching or parsing Notion page content for page ID ${pageId} from Splitbee API:`, error.message);
        if (error.response) {
            // Axios error with response
            console.error('Splitbee API Response Status:', error.response.status);
            console.error('Splitbee API Response Data:', error.response.data);
            throw new Error(`Splitbee API request failed with status ${error.response.status} for page ID ${pageId}.`);
        } else if (error.request) {
            // Axios error with no response (e.g., network error)
            throw new Error(`Splitbee API request failed for page ID ${pageId}. No response received (network error?).`);
        }
        // Other errors (e.g., parsing, logic errors)
        throw error;
    }
}

/**
 * Extracts the Notion page ID from various URL formats.
 * @param {string} url - The Notion page URL.
 * @returns {string|null} - The extracted page ID or null if not found.
 */
function extractNotionPageId(url) {
    // Handle different Notion URL formats:
    // https://www.notion.so/username/Page-Name-54c7c618172b4026a40ea94e584d0278
    // https://www.notion.so/Page-Name-54c7c618172b4026a40ea94e584d0278
    // https://username.notion.site/Page-Name-54c7c618172b4026a40ea94e584d0278
    // Also handles UUIDs directly if passed.

    if (!url || typeof url !== 'string') {
        return null;
    }

    // Regex to find a 32-character hex string (Notion ID) or a standard UUID
    // It looks for the ID at the end of the path, optionally followed by a query string
    const patterns = [
        /(?:[a-f0-9]{32})$/i, // 32 char hex string at the end
        /([a-f0-9]{32})(?:[?#]|$)/i, // 32 char hex string followed by query/hash or end
        /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})(?:[?#]|$)/i, // UUID
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match && match[1]) {
            let id = match[1];
            // If it's a 32-character ID, format it as a UUID for consistency with Notion API
            if (id.length === 32 && id.indexOf('-') === -1) {
                return `${id.slice(0, 8)}-${id.slice(8, 12)}-${id.slice(12, 16)}-${id.slice(16, 20)}-${id.slice(20)}`;
            }
            return id; // Already a UUID or a formatted ID
        }
    }

    // Fallback for URLs where the ID is the last path segment before a query string
    // e.g., https://acme.notion.site/My-Page-Title-54c7c618172b4026a40ea94e584d0278?v=...
    // This regex is more general for path segments.
    const complexUrlPattern = /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}|[a-f0-9]{32})(?:[?#]|$)/;
    const urlParts = url.split('/');
    const potentialId = urlParts.pop().split('?')[0].split('#')[0].split('-').pop();

    if (potentialId && complexUrlPattern.test(potentialId)) {
        let id = potentialId;
        if (id.length === 32 && id.indexOf('-') === -1) {
            return `${id.slice(0, 8)}-${id.slice(8, 12)}-${id.slice(12, 16)}-${id.slice(16, 20)}-${id.slice(20)}`;
        }
        // Check if it's a valid UUID structure if it contains hyphens
        if (id.indexOf('-') !== -1 && /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(id)) {
            return id;
        }
    }


    return null;
}

// --- Example Usage ---
async function main() {
    // Replace with your Notion page URL
    const notionPageUrl = 'https://everco.notion.site/PM-and-Time-Tracking-Software-54c7c618172b4026a40ea94e584d0278';
    // Or directly use a page ID:
    // const pageId = "54c7c618-172b-4026-a40e-a94e584d0278";


    console.log(`Attempting to extract ID from URL: ${notionPageUrl}`);
    const pageId = extractNotionPageId(notionPageUrl);

    if (pageId) {
        console.log(`Extracted Page ID: ${pageId}`);
        try {
            console.log('\nFetching Notion page content...');
            const content = await extractTextWithNotionAPI(pageId);
            console.log('\n--- Extracted Content ---');
            console.log(content);
            console.log('--- End of Content ---');
        } catch (error) {
            console.error('\nError during Notion content extraction:', error.message);
        }
    } else {
        console.error(`Could not extract Page ID from URL: ${notionPageUrl}`);
    }
}

// To run this example:
// 1. Make sure you have axios installed: pnpm install axios (if not already in project)
// 2. Run the script: node apps/api/test-notion-extraction.js
if (require.main === module) {
    // This check ensures main() runs only when the script is executed directly
    main().catch(err => {
        console.error("Unhandled error in main execution:", err);
        process.exit(1);
    });
}

// Export functions if you want to use them as a module
module.exports = {
    extractNotionPageId,
    extractTextWithNotionAPI,
    getTextFromBlock, // Exporting for potential advanced use or testing
    getTextFromTitleProperty
};